os:
  - linux

language: php

before_install:
  - wget https://phar.io/releases/phive.phar
  - wget https://phar.io/releases/phive.phar.asc
  - gpg --keyserver hkps.pool.sks-keyservers.net --recv-keys 0x9B2D5D79
  - gpg --verify phive.phar.asc phive.phar
  - chmod +x phive.phar
  - sudo mv phive.phar /usr/bin/phive

install:
  - ant setup

script: ./tools/phpunit

php:
  - 5.6
  - 7.0
  - 7.1
  - 7.0snapshot
  - 7.1snapshot
  - master

matrix:
  allow_failures:
    - php: master
  fast_finish: true

notifications:
  email: false
