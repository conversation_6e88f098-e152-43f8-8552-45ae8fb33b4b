O:33:"TheSeer\Tokenizer\TokenCollection":2:{s:41:" TheSeer\Tokenizer\TokenCollection tokens";a:158:{i:0;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:10:"T_OPEN_TAG";s:30:" TheSeer\Tokenizer\Token value";s:6:"<?php ";}i:1;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:9:"T_DECLARE";s:30:" TheSeer\Tokenizer\Token value";s:7:"declare";}i:2;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:14:"T_OPEN_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:"(";}i:3;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:12:"strict_types";}i:4;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:5;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:7:"T_EQUAL";s:30:" TheSeer\Tokenizer\Token value";s:1:"=";}i:6;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:7;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:9:"T_LNUMBER";s:30:" TheSeer\Tokenizer\Token value";s:1:"1";}i:8;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:15:"T_CLOSE_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:")";}i:9;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:11:"T_SEMICOLON";s:30:" TheSeer\Tokenizer\Token value";s:1:";";}i:10;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:1;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:11;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:2;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:12;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:2;s:29:" TheSeer\Tokenizer\Token name";s:11:"T_NAMESPACE";s:30:" TheSeer\Tokenizer\Token value";s:9:"namespace";}i:13;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:2;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:14;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:2;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:3:"foo";}i:15;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:2;s:29:" TheSeer\Tokenizer\Token name";s:11:"T_SEMICOLON";s:30:" TheSeer\Tokenizer\Token value";s:1:";";}i:16;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:2;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:17;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:3;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:18;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:4;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:19;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:4;s:29:" TheSeer\Tokenizer\Token name";s:7:"T_CLASS";s:30:" TheSeer\Tokenizer\Token value";s:5:"class";}i:20;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:4;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:21;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:4;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:3:"bar";}i:22;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:4;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:23;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:4;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_OPEN_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"{";}i:24;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:4;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:25;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:26;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:7:"T_CONST";s:30:" TheSeer\Tokenizer\Token value";s:5:"const";}i:27;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:28;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:1:"x";}i:29;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:30;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:7:"T_EQUAL";s:30:" TheSeer\Tokenizer\Token value";s:1:"=";}i:31;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:32;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:26:"T_CONSTANT_ENCAPSED_STRING";s:30:" TheSeer\Tokenizer\Token value";s:5:"'abc'";}i:33;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:11:"T_SEMICOLON";s:30:" TheSeer\Tokenizer\Token value";s:1:";";}i:34;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:5;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:35;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:6;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:36;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:7;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:37;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:7;s:29:" TheSeer\Tokenizer\Token name";s:13:"T_DOC_COMMENT";s:30:" TheSeer\Tokenizer\Token value";s:15:"/** @var int */";}i:38;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:7;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:39;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:40;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:9:"T_PRIVATE";s:30:" TheSeer\Tokenizer\Token value";s:7:"private";}i:41;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:42;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:10:"T_VARIABLE";s:30:" TheSeer\Tokenizer\Token value";s:2:"$y";}i:43;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:44;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:7:"T_EQUAL";s:30:" TheSeer\Tokenizer\Token value";s:1:"=";}i:45;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:46;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:9:"T_LNUMBER";s:30:" TheSeer\Tokenizer\Token value";s:1:"1";}i:47;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:11:"T_SEMICOLON";s:30:" TheSeer\Tokenizer\Token value";s:1:";";}i:48;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:8;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:49;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:9;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:50;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:51;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_PUBLIC";s:30:" TheSeer\Tokenizer\Token value";s:6:"public";}i:52;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:53;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:10:"T_FUNCTION";s:30:" TheSeer\Tokenizer\Token value";s:8:"function";}i:54;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:55;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:11:"__construct";}i:56;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:14:"T_OPEN_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:"(";}i:57;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:15:"T_CLOSE_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:")";}i:58;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:59;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_OPEN_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"{";}i:60;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:10;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:61;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:11;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:8:"        ";}i:62;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:11;s:29:" TheSeer\Tokenizer\Token name";s:9:"T_COMMENT";s:30:" TheSeer\Tokenizer\Token value";s:15:"// do something";}i:63;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:12;s:29:" TheSeer\Tokenizer\Token name";s:9:"T_COMMENT";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:64;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:12;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:65;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:12;s:29:" TheSeer\Tokenizer\Token name";s:13:"T_CLOSE_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"}";}i:66;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:12;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:67;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:13;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:68;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:69;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_PUBLIC";s:30:" TheSeer\Tokenizer\Token value";s:6:"public";}i:70;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:71;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:10:"T_FUNCTION";s:30:" TheSeer\Tokenizer\Token value";s:8:"function";}i:72;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:73;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:4:"getY";}i:74;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:14:"T_OPEN_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:"(";}i:75;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:15:"T_CLOSE_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:")";}i:76;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:7:"T_COLON";s:30:" TheSeer\Tokenizer\Token value";s:1:":";}i:77;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:78;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:3:"int";}i:79;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:80;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_OPEN_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"{";}i:81;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:14;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:82;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:15;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:8:"        ";}i:83;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:15;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_RETURN";s:30:" TheSeer\Tokenizer\Token value";s:6:"return";}i:84;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:15;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:85;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:15;s:29:" TheSeer\Tokenizer\Token name";s:10:"T_VARIABLE";s:30:" TheSeer\Tokenizer\Token value";s:5:"$this";}i:86;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:15;s:29:" TheSeer\Tokenizer\Token name";s:17:"T_OBJECT_OPERATOR";s:30:" TheSeer\Tokenizer\Token value";s:2:"->";}i:87;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:15;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:1:"y";}i:88;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:15;s:29:" TheSeer\Tokenizer\Token name";s:11:"T_SEMICOLON";s:30:" TheSeer\Tokenizer\Token value";s:1:";";}i:89;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:15;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:90;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:16;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:91;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:16;s:29:" TheSeer\Tokenizer\Token name";s:13:"T_CLOSE_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"}";}i:92;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:16;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:93;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:17;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:94;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:95;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_PUBLIC";s:30:" TheSeer\Tokenizer\Token value";s:6:"public";}i:96;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:97;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:10:"T_FUNCTION";s:30:" TheSeer\Tokenizer\Token value";s:8:"function";}i:98;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:99;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:8:"getSomeX";}i:100;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:14:"T_OPEN_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:"(";}i:101;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:15:"T_CLOSE_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:")";}i:102;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:7:"T_COLON";s:30:" TheSeer\Tokenizer\Token value";s:1:":";}i:103;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:104;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:6:"string";}i:105;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:106;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_OPEN_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"{";}i:107;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:18;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:108;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:19;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:8:"        ";}i:109;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:19;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_RETURN";s:30:" TheSeer\Tokenizer\Token value";s:6:"return";}i:110;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:19;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:111;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:19;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:4:"self";}i:112;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:19;s:29:" TheSeer\Tokenizer\Token name";s:14:"T_DOUBLE_COLON";s:30:" TheSeer\Tokenizer\Token value";s:2:"::";}i:113;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:19;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:1:"x";}i:114;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:19;s:29:" TheSeer\Tokenizer\Token name";s:11:"T_SEMICOLON";s:30:" TheSeer\Tokenizer\Token value";s:1:";";}i:115;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:19;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:116;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:20;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:117;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:20;s:29:" TheSeer\Tokenizer\Token name";s:13:"T_CLOSE_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"}";}i:118;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:20;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:119;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:21;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:120;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:121;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_PUBLIC";s:30:" TheSeer\Tokenizer\Token value";s:6:"public";}i:122;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:123;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:10:"T_FUNCTION";s:30:" TheSeer\Tokenizer\Token value";s:8:"function";}i:124;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:125;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:4:"some";}i:126;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:14:"T_OPEN_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:"(";}i:127;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:3:"bar";}i:128;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:129;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:10:"T_VARIABLE";s:30:" TheSeer\Tokenizer\Token value";s:2:"$b";}i:130;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:15:"T_CLOSE_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:")";}i:131;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:7:"T_COLON";s:30:" TheSeer\Tokenizer\Token value";s:1:":";}i:132;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:133;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:6:"string";}i:134;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:135;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_OPEN_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"{";}i:136;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:22;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:137;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:8:"        ";}i:138;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_RETURN";s:30:" TheSeer\Tokenizer\Token value";s:6:"return";}i:139;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:140;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:10:"T_VARIABLE";s:30:" TheSeer\Tokenizer\Token value";s:2:"$b";}i:141;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:17:"T_OBJECT_OPERATOR";s:30:" TheSeer\Tokenizer\Token value";s:2:"->";}i:142;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:8:"T_STRING";s:30:" TheSeer\Tokenizer\Token value";s:8:"getSomeX";}i:143;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:14:"T_OPEN_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:"(";}i:144;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:15:"T_CLOSE_BRACKET";s:30:" TheSeer\Tokenizer\Token value";s:1:")";}i:145;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:146;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:5:"T_DOT";s:30:" TheSeer\Tokenizer\Token value";s:1:".";}i:147;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:1:" ";}i:148;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:26:"T_CONSTANT_ENCAPSED_STRING";s:30:" TheSeer\Tokenizer\Token value";s:6:"'-def'";}i:149;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:11:"T_SEMICOLON";s:30:" TheSeer\Tokenizer\Token value";s:1:";";}i:150;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:23;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:151;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:24;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:4:"    ";}i:152;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:24;s:29:" TheSeer\Tokenizer\Token name";s:13:"T_CLOSE_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"}";}i:153;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:24;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:154;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:25;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:155;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:25;s:29:" TheSeer\Tokenizer\Token name";s:13:"T_CLOSE_CURLY";s:30:" TheSeer\Tokenizer\Token value";s:1:"}";}i:156;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:25;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}i:157;O:23:"TheSeer\Tokenizer\Token":3:{s:29:" TheSeer\Tokenizer\Token line";i:26;s:29:" TheSeer\Tokenizer\Token name";s:12:"T_WHITESPACE";s:30:" TheSeer\Tokenizer\Token value";s:0:"";}}s:38:" TheSeer\Tokenizer\TokenCollection pos";N;}