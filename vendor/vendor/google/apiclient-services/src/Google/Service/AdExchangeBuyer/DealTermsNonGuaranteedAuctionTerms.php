<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_AdExchangeBuyer_DealTermsNonGuaranteedAuctionTerms extends Google_Collection
{
  protected $collection_key = 'reservePricePerBuyers';
  public $autoOptimizePrivateAuction;
  protected $reservePricePerBuyersType = 'Google_Service_AdExchangeBuyer_PricePerBuyer';
  protected $reservePricePerBuyersDataType = 'array';

  public function setAutoOptimizePrivateAuction($autoOptimizePrivateAuction)
  {
    $this->autoOptimizePrivateAuction = $autoOptimizePrivateAuction;
  }
  public function getAutoOptimizePrivateAuction()
  {
    return $this->autoOptimizePrivateAuction;
  }
  /**
   * @param Google_Service_AdExchangeBuyer_PricePerBuyer
   */
  public function setReservePricePerBuyers($reservePricePerBuyers)
  {
    $this->reservePricePerBuyers = $reservePricePerBuyers;
  }
  /**
   * @return Google_Service_AdExchangeBuyer_PricePerBuyer
   */
  public function getReservePricePerBuyers()
  {
    return $this->reservePricePerBuyers;
  }
}
